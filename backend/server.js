const express = require('express');
const Iyzipay = require('iyzipay');

const app = express();

const iyzipay = new Iyzipay({
    apiKey: process.env.IYZIPAY_API_KEY,
    secretKey: process.env.IYZIPAY_SECRET_KEY,
    uri: 'https://sandbox-api.iyzipay.com'
});

app.post('/api/payments', async (req, res) => {
    try {
        const {
            userId,
            amount,
            cardNumber,
            cardHolderName,
            expirationMonth,
            expirationYear,
            cvc
        } = req.body;

        const request = {
            locale: 'tr',
            conversationId: '123456789',
            price: amount,
            paidPrice: amount,
            currency: 'TRY',
            installment: '1',
            basketId: 'B67832',
            paymentChannel: 'WEB',
            paymentGroup: 'PRODUCT',
            paymentCard: {
                cardHolderName: cardHolderName,
                cardNumber: cardNumber,
                expireMonth: expirationMonth,
                expireYear: expirationYear,
                cvc: cvc,
                registerCard: '0'
            },
            buyer: {
                id: userId,
                name: '<PERSON>',
                surname: '<PERSON><PERSON>',
                // ... diğer zorun<PERSON> al<PERSON>
            },
            // ... diğer zorunlu alanlar
        };

        iyzipay.payment.create(request, function (err, result) {
            if (err) {
                res.status(400).json({ success: false, error: err });
            } else {
                res.json({ 
                    success: result.status === 'success',
                    message: result.status === 'success' ? 'Ödeme başarılı' : 'Ödeme başarısız'
                });
            }
        });
    } catch (error) {
        res.status(500).json({ success: false, error: error.message });
    }
});

app.listen(3000, () => {
    console.log('Server running on port 3000');
}); 