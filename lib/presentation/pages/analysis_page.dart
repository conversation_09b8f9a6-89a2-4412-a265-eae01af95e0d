import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:hekimmind/presentation/blocs/analysis/analysis_bloc.dart';
import 'package:hekimmind/presentation/blocs/analysis/analysis_event.dart';
import 'package:hekimmind/presentation/blocs/analysis/analysis_state.dart';
import 'package:hekimmind/core/di/injection.dart';
import 'package:hekimmind/data/models/patient_data_model.dart';

class AnalysisPage extends StatefulWidget {
  final String analysisType;

  const AnalysisPage({
    super.key,
    required this.analysisType,
  });

  @override
  State<AnalysisPage> createState() => _AnalysisPageState();
}

class _AnalysisPageState extends State<AnalysisPage> {
  File? _selectedImage;
  final _imagePicker = ImagePicker();
  final _formKey = GlobalKey<FormState>();

  // Form alanları için controller'lar
  final _ageController = TextEditingController();
  String _gender = 'Erkek'; // <PERSON><PERSON><PERSON><PERSON><PERSON> değer
  final _symptomsController = TextEditingController();
  final _medicalHistoryController = TextEditingController();
  final _medicationsController = TextEditingController();
  final _additionalNotesController = TextEditingController();

  // Yeni eklenen controller'lar
  final _heightController = TextEditingController();
  final _weightController = TextEditingController();
  final _familyHistoryController = TextEditingController();
  final _symptomDurationController = TextEditingController();
  final _allergiesController = TextEditingController();
  final _labResultsController = TextEditingController();
  final _otherConditionsController = TextEditingController();
  final _reasonForExamController = TextEditingController();

  // Yaşam tarzı seçenekleri
  bool _smoker = false;
  bool _alcohol = false;
  bool _physicallyActive = false;

  bool _formExpanded = false;
  int _currentFormSection = 0;
  final List<String> _formSections = [
    'Temel Bilgiler',
    'Sağlık Geçmişi',
    'Güncel Durum',
    'Yaşam Tarzı',
  ];

  @override
  void dispose() {
    _ageController.dispose();
    _symptomsController.dispose();
    _medicalHistoryController.dispose();
    _medicationsController.dispose();
    _additionalNotesController.dispose();

    // Yeni eklenen controller'ların dispose edilmesi
    _heightController.dispose();
    _weightController.dispose();
    _familyHistoryController.dispose();
    _symptomDurationController.dispose();
    _allergiesController.dispose();
    _labResultsController.dispose();
    _otherConditionsController.dispose();
    _reasonForExamController.dispose();

    super.dispose();
  }

  Future<void> _pickImage() async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.gallery,
      maxWidth: 1200,
      maxHeight: 1200,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
      });
    }
  }

  Future<void> _takePicture() async {
    final pickedFile = await _imagePicker.pickImage(
      source: ImageSource.camera,
      maxWidth: 1200,
      maxHeight: 1200,
    );

    if (pickedFile != null) {
      setState(() {
        _selectedImage = File(pickedFile.path);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final user = FirebaseAuth.instance.currentUser;
    Color themeColor = _getThemeColorForAnalysisType(widget.analysisType);

    return BlocProvider(
      create: (context) => AnalysisBloc(
        analysisRepository: getIt(),
        coinRepository: getIt(),
      )..add(LoadAnalysisHistory(user?.uid ?? '')),
      child: Scaffold(
        backgroundColor: Colors.grey[50],
        appBar: AppBar(
          title: Text(
            '${widget.analysisType} Analizi',
            style: TextStyle(
              color: themeColor,
              fontWeight: FontWeight.bold,
            ),
          ),
          elevation: 0,
          backgroundColor: Colors.white,
          iconTheme: IconThemeData(color: themeColor),
        ),
        body: BlocConsumer<AnalysisBloc, AnalysisState>(
          listener: (context, state) {
            if (state is AnalysisError) {
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text(state.message),
                  backgroundColor: Colors.red.shade700,
                ),
              );
            }
          },
          builder: (context, state) {
            return SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(20.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    // Görüntü Yükleme Alanı
                    Container(
                      height: 280,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(20),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                        border: Border.all(
                          color: Colors.grey.shade200,
                          width: 1,
                        ),
                      ),
                      child: _selectedImage != null
                          ? Stack(
                              fit: StackFit.expand,
                              children: [
                                ClipRRect(
                                  borderRadius: BorderRadius.circular(20),
                                  child: Image.file(
                                    _selectedImage!,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                                Positioned(
                                  top: 10,
                                  right: 10,
                                  child: InkWell(
                                    onTap: () {
                                      setState(() {
                                        _selectedImage = null;
                                      });
                                    },
                                    child: Container(
                                      padding: const EdgeInsets.all(8),
                                      decoration: BoxDecoration(
                                        color: Colors.white.withOpacity(0.8),
                                        shape: BoxShape.circle,
                                      ),
                                      child: Icon(
                                        Icons.close,
                                        color: themeColor,
                                        size: 20,
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            )
                          : Column(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  _getIconForAnalysisType(widget.analysisType),
                                  size: 70,
                                  color: themeColor.withOpacity(0.3),
                                ),
                                const SizedBox(height: 16),
                                Text(
                                  '${widget.analysisType} görüntüsü yükleyin',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                                const SizedBox(height: 8),
                                Text(
                                  'AI destekli analiz için bir görüntü gerekli',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.grey.shade400,
                                  ),
                                ),
                              ],
                            ),
                    ),
                    const SizedBox(height: 24),

                    // Görüntü Seçme Butonları
                    Row(
                      children: [
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _pickImage,
                            icon: const Icon(Icons.photo_library_outlined),
                            label: const Text('Galeriden Seç'),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: Colors.white,
                              backgroundColor: themeColor,
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: ElevatedButton.icon(
                            onPressed: _takePicture,
                            icon: const Icon(Icons.camera_alt_outlined),
                            label: const Text('Kamera'),
                            style: ElevatedButton.styleFrom(
                              foregroundColor: themeColor,
                              backgroundColor: themeColor.withOpacity(0.1),
                              padding: const EdgeInsets.symmetric(vertical: 12),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // Hasta Bilgileri Form Alanı
                    AnimatedContainer(
                      duration: const Duration(milliseconds: 300),
                      curve: Curves.easeInOut,
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withOpacity(0.1),
                            blurRadius: 10,
                            spreadRadius: 2,
                          ),
                        ],
                      ),
                      child: Padding(
                        padding: const EdgeInsets.all(16.0),
                        child: Form(
                          key: _formKey,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Form Başlığı
                              Row(
                                mainAxisAlignment:
                                    MainAxisAlignment.spaceBetween,
                                children: [
                                  Row(
                                    children: [
                                      Icon(
                                        Icons.person,
                                        color: themeColor,
                                        size: 20,
                                      ),
                                      const SizedBox(width: 8),
                                      const Text(
                                        'Hasta Bilgileri',
                                        style: TextStyle(
                                          fontSize: 18,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                  IconButton(
                                    icon: Icon(
                                      _formExpanded
                                          ? Icons.keyboard_arrow_up
                                          : Icons.keyboard_arrow_down,
                                      color: Colors.grey.shade600,
                                    ),
                                    onPressed: () {
                                      setState(() {
                                        _formExpanded = !_formExpanded;
                                      });
                                    },
                                  ),
                                ],
                              ),

                              const SizedBox(height: 12),

                              // Form Bölüm Sekmeleri
                              if (_formExpanded)
                                Container(
                                  height: 50,
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade50,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: ListView.builder(
                                    scrollDirection: Axis.horizontal,
                                    itemCount: _formSections.length,
                                    itemBuilder: (context, index) {
                                      return GestureDetector(
                                        onTap: () {
                                          setState(() {
                                            _currentFormSection = index;
                                          });
                                        },
                                        child: Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 16),
                                          margin: const EdgeInsets.all(4),
                                          decoration: BoxDecoration(
                                            color: _currentFormSection == index
                                                ? themeColor
                                                : Colors.transparent,
                                            borderRadius:
                                                BorderRadius.circular(10),
                                          ),
                                          child: Center(
                                            child: Text(
                                              _formSections[index],
                                              style: TextStyle(
                                                fontSize: 14,
                                                fontWeight:
                                                    _currentFormSection == index
                                                        ? FontWeight.bold
                                                        : FontWeight.normal,
                                                color:
                                                    _currentFormSection == index
                                                        ? Colors.white
                                                        : Colors.grey.shade700,
                                              ),
                                            ),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                ),

                              if (!_formExpanded ||
                                  _currentFormSection == 0) ...[
                                const SizedBox(height: 16),

                                // Yaş ve Cinsiyet (zorunlu alanlar)
                                Row(
                                  children: [
                                    // Yaş
                                    Expanded(
                                      flex: 2,
                                      child: TextFormField(
                                        controller: _ageController,
                                        decoration: InputDecoration(
                                          labelText: 'Yaş*',
                                          hintText: '35',
                                          prefixIcon: Icon(
                                            Icons.calendar_today,
                                            size: 20,
                                            color: themeColor,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                            vertical: 16,
                                            horizontal: 12,
                                          ),
                                        ),
                                        keyboardType: TextInputType.number,
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Yaş zorunludur';
                                          }
                                          if (int.tryParse(value) == null) {
                                            return 'Geçerli bir yaş girin';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    // Cinsiyet
                                    Expanded(
                                      flex: 3,
                                      child: DropdownButtonFormField<String>(
                                        value: _gender,
                                        decoration: InputDecoration(
                                          labelText: 'Cinsiyet*',
                                          prefixIcon: Icon(
                                            Icons.people_alt_outlined,
                                            size: 20,
                                            color: themeColor,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                          contentPadding:
                                              const EdgeInsets.symmetric(
                                            vertical: 16,
                                            horizontal: 12,
                                          ),
                                        ),
                                        items: ['Erkek', 'Kadın', 'Diğer']
                                            .map<DropdownMenuItem<String>>(
                                          (String value) {
                                            return DropdownMenuItem<String>(
                                              value: value,
                                              child: Text(value),
                                            );
                                          },
                                        ).toList(),
                                        onChanged: (String? newValue) {
                                          if (newValue != null) {
                                            setState(() {
                                              _gender = newValue;
                                            });
                                          }
                                        },
                                        validator: (value) {
                                          if (value == null || value.isEmpty) {
                                            return 'Cinsiyet zorunludur';
                                          }
                                          return null;
                                        },
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                // Boy ve Kilo
                                Row(
                                  children: [
                                    // Boy
                                    Expanded(
                                      child: TextFormField(
                                        controller: _heightController,
                                        decoration: InputDecoration(
                                          labelText: 'Boy (cm)',
                                          hintText: '175',
                                          prefixIcon: Icon(
                                            Icons.height,
                                            size: 20,
                                            color: themeColor,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                        ),
                                        keyboardType: TextInputType.number,
                                      ),
                                    ),
                                    const SizedBox(width: 16),
                                    // Kilo
                                    Expanded(
                                      child: TextFormField(
                                        controller: _weightController,
                                        decoration: InputDecoration(
                                          labelText: 'Kilo (kg)',
                                          hintText: '70',
                                          prefixIcon: Icon(
                                            Icons.monitor_weight_outlined,
                                            size: 20,
                                            color: themeColor,
                                          ),
                                          border: OutlineInputBorder(
                                            borderRadius:
                                                BorderRadius.circular(12),
                                          ),
                                        ),
                                        keyboardType: TextInputType.number,
                                      ),
                                    ),
                                  ],
                                ),

                                const SizedBox(height: 16),

                                // Tetkikin Çekilme Nedeni
                                TextFormField(
                                  controller: _reasonForExamController,
                                  decoration: InputDecoration(
                                    labelText:
                                        '${widget.analysisType} Çekilme Nedeni',
                                    hintText:
                                        'Baş ağrısı şikayeti sebebiyle...',
                                    prefixIcon: Icon(
                                      Icons.help_outline,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),
                              ],

                              // Sağlık Geçmişi Sekmesi
                              if (_formExpanded &&
                                  _currentFormSection == 1) ...[
                                const SizedBox(height: 16),

                                // Aile Sağlık Geçmişi
                                TextFormField(
                                  controller: _familyHistoryController,
                                  decoration: InputDecoration(
                                    labelText: 'Aile Sağlık Geçmişi',
                                    hintText: 'Ailedeki genetik hastalıklar...',
                                    prefixIcon: Icon(
                                      Icons.family_restroom,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  maxLines: 2,
                                ),

                                const SizedBox(height: 16),

                                // Tıbbi Geçmiş
                                TextFormField(
                                  controller: _medicalHistoryController,
                                  decoration: InputDecoration(
                                    labelText: 'Tıbbi Geçmiş',
                                    hintText:
                                        'Önceki hastalıklar, ameliyatlar...',
                                    prefixIcon: Icon(
                                      Icons.history_edu,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  maxLines: 2,
                                ),

                                const SizedBox(height: 16),

                                // Alerjiler
                                TextFormField(
                                  controller: _allergiesController,
                                  decoration: InputDecoration(
                                    labelText: 'Alerjiler',
                                    hintText: 'İlaç veya diğer alerjiler...',
                                    prefixIcon: Icon(
                                      Icons.coronavirus_outlined,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Diğer Hastalıklar
                                TextFormField(
                                  controller: _otherConditionsController,
                                  decoration: InputDecoration(
                                    labelText: 'Eşlik Eden Hastalıklar',
                                    hintText:
                                        'Diyabet, hipertansiyon, kalp hastalığı...',
                                    prefixIcon: Icon(
                                      Icons.local_hospital_outlined,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  maxLines: 2,
                                ),
                              ],

                              // Güncel Durum Sekmesi
                              if (_formExpanded &&
                                  _currentFormSection == 2) ...[
                                const SizedBox(height: 16),

                                // Belirtiler
                                TextFormField(
                                  controller: _symptomsController,
                                  decoration: InputDecoration(
                                    labelText: 'Belirtiler',
                                    hintText: 'Baş ağrısı, baş dönmesi vb.',
                                    prefixIcon: Icon(
                                      Icons.sick_outlined,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  maxLines: 2,
                                ),

                                const SizedBox(height: 16),

                                // Şikayetin Süresi
                                TextFormField(
                                  controller: _symptomDurationController,
                                  decoration: InputDecoration(
                                    labelText: 'Şikayetin Süresi',
                                    hintText: '3 aydır, 2 haftadır...',
                                    prefixIcon: Icon(
                                      Icons.date_range_outlined,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Kullanılan İlaçlar
                                TextFormField(
                                  controller: _medicationsController,
                                  decoration: InputDecoration(
                                    labelText: 'Kullanılan İlaçlar',
                                    hintText: 'Şu an kullanılan ilaçlar',
                                    prefixIcon: Icon(
                                      Icons.medication_outlined,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  maxLines: 2,
                                ),

                                const SizedBox(height: 16),

                                // Tahlil Sonuçları
                                TextFormField(
                                  controller: _labResultsController,
                                  decoration: InputDecoration(
                                    labelText: 'Tahlil Sonuçları',
                                    hintText:
                                        'Kan değerleri, tansiyon, şeker ölçümleri...',
                                    prefixIcon: Icon(
                                      Icons.science_outlined,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  maxLines: 3,
                                ),
                              ],

                              // Yaşam Tarzı Sekmesi
                              if (_formExpanded &&
                                  _currentFormSection == 3) ...[
                                const SizedBox(height: 16),

                                // Yaşam Tarzı Seçenekleri
                                Container(
                                  padding: const EdgeInsets.all(16),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.shade50,
                                    borderRadius: BorderRadius.circular(12),
                                    border: Border.all(
                                      color: Colors.grey.shade200,
                                      width: 1,
                                    ),
                                  ),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      const Text(
                                        'Yaşam Tarzı',
                                        style: TextStyle(
                                          fontSize: 16,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                      const SizedBox(height: 12),

                                      // Sigara
                                      CheckboxListTile(
                                        contentPadding: EdgeInsets.zero,
                                        title: const Text('Sigara Kullanımı'),
                                        subtitle: const Text(
                                            'Hasta sigara kullanıyor mu?'),
                                        value: _smoker,
                                        onChanged: (bool? value) {
                                          setState(() {
                                            _smoker = value ?? false;
                                          });
                                        },
                                        activeColor: themeColor,
                                        checkColor: Colors.white,
                                        dense: true,
                                        visualDensity: VisualDensity.compact,
                                      ),

                                      // Alkol
                                      CheckboxListTile(
                                        contentPadding: EdgeInsets.zero,
                                        title: const Text('Alkol Kullanımı'),
                                        subtitle: const Text(
                                            'Hasta alkol kullanıyor mu?'),
                                        value: _alcohol,
                                        onChanged: (bool? value) {
                                          setState(() {
                                            _alcohol = value ?? false;
                                          });
                                        },
                                        activeColor: themeColor,
                                        checkColor: Colors.white,
                                        dense: true,
                                        visualDensity: VisualDensity.compact,
                                      ),

                                      // Fiziksel Aktivite
                                      CheckboxListTile(
                                        contentPadding: EdgeInsets.zero,
                                        title: const Text('Fiziksel Aktivite'),
                                        subtitle: const Text(
                                            'Hasta düzenli egzersiz yapıyor mu?'),
                                        value: _physicallyActive,
                                        onChanged: (bool? value) {
                                          setState(() {
                                            _physicallyActive = value ?? false;
                                          });
                                        },
                                        activeColor: themeColor,
                                        checkColor: Colors.white,
                                        dense: true,
                                        visualDensity: VisualDensity.compact,
                                      ),
                                    ],
                                  ),
                                ),

                                const SizedBox(height: 16),

                                // Ek Notlar
                                TextFormField(
                                  controller: _additionalNotesController,
                                  decoration: InputDecoration(
                                    labelText: 'Ek Notlar',
                                    hintText:
                                        'Yaşam tarzı ile ilgili ek bilgiler...',
                                    prefixIcon: Icon(
                                      Icons.note_alt_outlined,
                                      size: 20,
                                      color: themeColor,
                                    ),
                                    border: OutlineInputBorder(
                                      borderRadius: BorderRadius.circular(12),
                                    ),
                                  ),
                                  maxLines: 3,
                                ),
                              ],

                              // Form bölümü dipnotu
                              if (_formExpanded)
                                Padding(
                                  padding: const EdgeInsets.only(top: 16),
                                  child: Row(
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        size: 16,
                                        color: Colors.grey.shade500,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'Sadece yaş ve cinsiyet zorunludur. Diğer bilgiler AI analizin doğruluğunu artırabilir.',
                                          style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade500,
                                            fontStyle: FontStyle.italic,
                                          ),
                                        ),
                                      ),
                                    ],
                                  ),
                                ),

                              // Form sekmelerinde gezinme butonları
                              if (_formExpanded)
                                Padding(
                                  padding: const EdgeInsets.only(top: 16),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      // Önceki buton
                                      if (_currentFormSection > 0)
                                        OutlinedButton.icon(
                                          onPressed: () {
                                            setState(() {
                                              _currentFormSection--;
                                            });
                                          },
                                          icon: const Icon(Icons.arrow_back_ios,
                                              size: 16),
                                          label: const Text('Önceki'),
                                          style: OutlinedButton.styleFrom(
                                            foregroundColor: themeColor,
                                            side: BorderSide(color: themeColor),
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 8,
                                            ),
                                          ),
                                        )
                                      else
                                        const SizedBox(),

                                      // Sonraki/Bitir buton
                                      if (_currentFormSection <
                                          _formSections.length - 1)
                                        ElevatedButton.icon(
                                          onPressed: () {
                                            setState(() {
                                              _currentFormSection++;
                                            });
                                          },
                                          icon: const Icon(
                                              Icons.arrow_forward_ios,
                                              size: 16),
                                          label: const Text('Sonraki'),
                                          style: ElevatedButton.styleFrom(
                                            foregroundColor: Colors.white,
                                            backgroundColor: themeColor,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 8,
                                            ),
                                          ),
                                        )
                                      else
                                        ElevatedButton.icon(
                                          onPressed: () {
                                            setState(() {
                                              _formExpanded = false;
                                              _currentFormSection = 0;
                                            });
                                          },
                                          icon:
                                              const Icon(Icons.check, size: 16),
                                          label: const Text('Tamamla'),
                                          style: ElevatedButton.styleFrom(
                                            foregroundColor: Colors.white,
                                            backgroundColor:
                                                Colors.green.shade600,
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 12,
                                              vertical: 8,
                                            ),
                                          ),
                                        ),
                                    ],
                                  ),
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(height: 24),

                    // Analiz Butonu
                    if (_selectedImage != null)
                      SizedBox(
                        height: 54,
                        child: ElevatedButton(
                          onPressed: state is AnalysisLoading
                              ? null
                              : () {
                                  if (_formKey.currentState!.validate()) {
                                    // Hasta bilgilerini topla
                                    final patientData = PatientDataModel(
                                      age: _ageController.text.trim(),
                                      gender: _gender,
                                      height: _heightController.text.trim(),
                                      weight: _weightController.text.trim(),
                                      symptoms: _symptomsController.text.trim(),
                                      medicalHistory: _medicalHistoryController.text.trim(),
                                      medications: _medicationsController.text.trim(),
                                      familyHistory: _familyHistoryController.text.trim(),
                                      symptomDuration: _symptomDurationController.text.trim(),
                                      allergies: _allergiesController.text.trim(),
                                      labResults: _labResultsController.text.trim(),
                                      otherConditions: _otherConditionsController.text.trim(),
                                      reasonForExam: _reasonForExamController.text.trim(),
                                      additionalNotes: _additionalNotesController.text.trim(),
                                      smoker: _smoker,
                                      alcohol: _alcohol,
                                      physicallyActive: _physicallyActive,
                                      timestamp: DateTime.now(),
                                    );

                                    // Analiz başlat
                                    context.read<AnalysisBloc>().add(
                                          UploadAndAnalyzeImage(
                                            userId: user?.uid ?? '',
                                            image: _selectedImage!,
                                            analysisType: widget.analysisType,
                                            patientData: patientData,
                                          ),
                                        );
                                  }
                                },
                          style: ElevatedButton.styleFrom(
                            foregroundColor: Colors.white,
                            backgroundColor: themeColor,
                            disabledBackgroundColor: Colors.grey.shade300,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(16),
                            ),
                            elevation: 2,
                          ),
                          child: state is AnalysisLoading
                              ? Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    SizedBox(
                                      width: 20,
                                      height: 20,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                        color: Colors.white,
                                      ),
                                    ),
                                    const SizedBox(width: 12),
                                    const Text('Analiz Ediliyor...'),
                                  ],
                                )
                              : Row(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    Icon(
                                      Icons.analytics,
                                      size: 20,
                                    ),
                                    const SizedBox(width: 10),
                                    const Text(
                                      'AI ile Analiz Et',
                                      style: TextStyle(
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                        ),
                      ),

                    const SizedBox(height: 30),

                    // Analiz Sonuçları
                    if (state is AnalysisSuccess) ...[
                      Row(
                        children: [
                          Icon(
                            Icons.analytics_outlined,
                            color: themeColor,
                          ),
                          const SizedBox(width: 8),
                          const Text(
                            'Analiz Sonucu',
                            style: TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      Container(
                        padding: const EdgeInsets.all(20),
                        decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(16),
                          boxShadow: [
                            BoxShadow(
                              color: themeColor.withOpacity(0.1),
                              blurRadius: 15,
                              offset: const Offset(0, 8),
                            ),
                          ],
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Tarih ve saat
                            Row(
                              children: [
                                Icon(
                                  Icons.timer_outlined,
                                  size: 16,
                                  color: Colors.grey.shade600,
                                ),
                                const SizedBox(width: 6),
                                Text(
                                  _formatDate(state.result.timestamp),
                                  style: TextStyle(
                                    fontSize: 13,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 12),
                            // Analiz içeriği
                            Text(
                              state.result.result,
                              style: const TextStyle(
                                fontSize: 16,
                                height: 1.5,
                              ),
                            ),
                            const SizedBox(height: 16),
                            // AI uyarısı
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.amber.withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.amber.withOpacity(0.3),
                                ),
                              ),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.info_outline,
                                    color: Colors.amber.shade800,
                                    size: 18,
                                  ),
                                  const SizedBox(width: 8),
                                  Expanded(
                                    child: Text(
                                      'Bu sonuç yapay zeka tarafından üretilmiştir. Kesin teşhis için lütfen uzman bir doktora danışınız.',
                                      style: TextStyle(
                                        fontSize: 13,
                                        color: Colors.amber.shade800,
                                        height: 1.4,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 20),
                            // Paylaş ve kaydet butonları
                            Row(
                              children: [
                                Expanded(
                                  child: OutlinedButton.icon(
                                    onPressed: () {
                                      // Paylaşma fonksiyonu
                                    },
                                    icon: const Icon(Icons.share_outlined),
                                    label: const Text('Paylaş'),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: themeColor,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 12),
                                Expanded(
                                  child: OutlinedButton.icon(
                                    onPressed: () {
                                      // Kaydetme fonksiyonu
                                    },
                                    icon: const Icon(Icons.download_outlined),
                                    label: const Text('Kaydet'),
                                    style: OutlinedButton.styleFrom(
                                      foregroundColor: themeColor,
                                      padding: const EdgeInsets.symmetric(
                                          vertical: 12),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(12),
                                      ),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),
                    ] else if (state is AnalysisHistoryLoaded &&
                        state.history.isNotEmpty) ...[
                      // Önceki Analizler
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.history,
                                color: themeColor,
                              ),
                              const SizedBox(width: 8),
                              const Text(
                                'Önceki Analizler',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          TextButton(
                            onPressed: () {
                              // Tüm analizleri görüntüleme sayfasına yönlendirme
                            },
                            child: const Text('Tümünü Gör'),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      ...state.history.take(3).map((analysis) => Container(
                            margin: const EdgeInsets.only(bottom: 12),
                            decoration: BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.circular(16),
                              boxShadow: [
                                BoxShadow(
                                  color: Colors.grey.withOpacity(0.1),
                                  blurRadius: 5,
                                  offset: const Offset(0, 2),
                                ),
                              ],
                            ),
                            child: ListTile(
                              contentPadding: const EdgeInsets.symmetric(
                                vertical: 12,
                                horizontal: 16,
                              ),
                              leading: Container(
                                width: 56,
                                height: 56,
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                  border: Border.all(
                                    color: Colors.grey.shade200,
                                  ),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: Image.network(
                                    analysis.imageUrl,
                                    fit: BoxFit.cover,
                                  ),
                                ),
                              ),
                              title: Text(
                                analysis.analysisType,
                                style: const TextStyle(
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              subtitle: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  const SizedBox(height: 4),
                                  Text(
                                    _formatDate(analysis.timestamp),
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    ),
                                  ),
                                ],
                              ),
                              trailing: Container(
                                width: 36,
                                height: 36,
                                decoration: BoxDecoration(
                                  color: themeColor.withOpacity(0.1),
                                  shape: BoxShape.circle,
                                ),
                                child: Icon(
                                  Icons.arrow_forward_ios,
                                  size: 14,
                                  color: themeColor,
                                ),
                              ),
                              onTap: () {
                                // Detay sayfasına yönlendirme
                              },
                            ),
                          )),
                    ],
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}.${date.month}.${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  Color _getThemeColorForAnalysisType(String type) {
    switch (type) {
      case 'MR':
        return const Color(0xFF2D5BFF);
      case 'EKG':
        return const Color(0xFFE63946);
      case 'EEG':
        return const Color(0xFF7209B7);
      default:
        return const Color(0xFF2D5BFF);
    }
  }

  IconData _getIconForAnalysisType(String type) {
    switch (type) {
      case 'MR':
        return Icons.medical_information;
      case 'EKG':
        return Icons.monitor_heart;
      case 'EEG':
        return Icons.psychology;
      default:
        return Icons.medical_services;
    }
  }
}
