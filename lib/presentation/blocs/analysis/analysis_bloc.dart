import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hekimmind/domain/repositories/analysis_repository.dart';
import 'package:hekimmind/domain/repositories/coin_repository.dart';
import 'package:hekimmind/presentation/blocs/analysis/analysis_event.dart';
import 'package:hekimmind/presentation/blocs/analysis/analysis_state.dart';

class AnalysisBloc extends Bloc<AnalysisEvent, AnalysisState> {
  final AnalysisRepository _analysisRepository;
  final CoinRepository _coinRepository;
  static const int ANALYSIS_COST = 1; // Her analiz için gerekli coin miktarı

  AnalysisBloc({
    required AnalysisRepository analysisRepository,
    required CoinRepository coinRepository,
  })  : _analysisRepository = analysisRepository,
        _coinRepository = coinRepository,
        super(AnalysisInitial()) {
    on<UploadAndAnalyzeImage>((event, emit) async {
      emit(AnalysisLoading());
      try {
        // Kullanıcının yeterli coini var mı kontrol et
        final balance = await _coinRepository.getCurrentBalance(event.userId);
        if (balance < ANALYSIS_COST) {
          emit(AnalysisError('Yetersiz kredi! Lütfen kredi satın alın.'));
          return;
        }

        // Görüntüyü yükle
        final imageUrl = await _analysisRepository.uploadImage(
          event.image,
          event.userId,
        );

        // Analiz yap
        final result = await _analysisRepository.analyzeImage(
          imageUrl,
          event.userId,
          event.analysisType,
          event.patientData,
        );

        // Coin düş
        await _coinRepository.useCoins(event.userId, ANALYSIS_COST);

        emit(AnalysisSuccess(result));
      } catch (e) {
        emit(AnalysisError(e.toString()));
      }
    });

    on<LoadAnalysisHistory>((event, emit) async {
      emit(AnalysisLoading());
      try {
        final history =
            await _analysisRepository.getAnalysisHistory(event.userId);
        emit(AnalysisHistoryLoaded(history));
      } catch (e) {
        emit(AnalysisError(e.toString()));
      }
    });
  }
}
