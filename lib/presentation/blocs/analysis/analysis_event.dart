import 'dart:io';
import 'package:hekimmind/data/models/patient_data_model.dart';

abstract class AnalysisEvent {}

class UploadAndAnalyzeImage extends AnalysisEvent {
  final String userId;
  final File image;
  final String analysisType;
  final PatientDataModel patientData;

  UploadAndAnalyzeImage({
    required this.userId,
    required this.image,
    required this.analysisType,
    required this.patientData,
  });
}

class LoadAnalysisHistory extends AnalysisEvent {
  final String userId;
  LoadAnalysisHistory(this.userId);
}
