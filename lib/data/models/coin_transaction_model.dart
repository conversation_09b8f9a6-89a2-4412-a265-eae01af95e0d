class CoinTransactionModel {
  final String id;
  final String userId;
  final int amount;
  final String type; // 'purchase', 'usage', 'reward'
  final DateTime timestamp;

  CoinTransactionModel({
    required this.id,
    required this.userId,
    required this.amount,
    required this.type,
    required this.timestamp,
  });

  factory CoinTransactionModel.fromJson(Map<String, dynamic> json) {
    return CoinTransactionModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      amount: json['amount'] as int,
      type: json['type'] as String,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'amount': amount,
      'type': type,
      'timestamp': timestamp.toIso8601String(),
    };
  }
}
