import 'package:equatable/equatable.dart';

class PatientDataModel extends Equatable {
  final String? age;
  final String gender;
  final String? height;
  final String? weight;
  final String? symptoms;
  final String? medicalHistory;
  final String? medications;
  final String? familyHistory;
  final String? symptomDuration;
  final String? allergies;
  final String? labResults;
  final String? otherConditions;
  final String? reasonForExam;
  final String? additionalNotes;
  final bool smoker;
  final bool alcohol;
  final bool physicallyActive;
  final DateTime timestamp;

  const PatientDataModel({
    this.age,
    required this.gender,
    this.height,
    this.weight,
    this.symptoms,
    this.medicalHistory,
    this.medications,
    this.familyHistory,
    this.symptomDuration,
    this.allergies,
    this.labResults,
    this.otherConditions,
    this.reasonForExam,
    this.additionalNotes,
    this.smoker = false,
    this.alcohol = false,
    this.physicallyActive = false,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() {
    return {
      'age': age,
      'gender': gender,
      'height': height,
      'weight': weight,
      'symptoms': symptoms,
      'medicalHistory': medicalHistory,
      'medications': medications,
      'familyHistory': familyHistory,
      'symptomDuration': symptomDuration,
      'allergies': allergies,
      'labResults': labResults,
      'otherConditions': otherConditions,
      'reasonForExam': reasonForExam,
      'additionalNotes': additionalNotes,
      'smoker': smoker,
      'alcohol': alcohol,
      'physicallyActive': physicallyActive,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PatientDataModel.fromJson(Map<String, dynamic> json) {
    return PatientDataModel(
      age: json['age'],
      gender: json['gender'] ?? 'Erkek',
      height: json['height'],
      weight: json['weight'],
      symptoms: json['symptoms'],
      medicalHistory: json['medicalHistory'],
      medications: json['medications'],
      familyHistory: json['familyHistory'],
      symptomDuration: json['symptomDuration'],
      allergies: json['allergies'],
      labResults: json['labResults'],
      otherConditions: json['otherConditions'],
      reasonForExam: json['reasonForExam'],
      additionalNotes: json['additionalNotes'],
      smoker: json['smoker'] ?? false,
      alcohol: json['alcohol'] ?? false,
      physicallyActive: json['physicallyActive'] ?? false,
      timestamp: DateTime.parse(json['timestamp']),
    );
  }

  PatientDataModel copyWith({
    String? age,
    String? gender,
    String? height,
    String? weight,
    String? symptoms,
    String? medicalHistory,
    String? medications,
    String? familyHistory,
    String? symptomDuration,
    String? allergies,
    String? labResults,
    String? otherConditions,
    String? reasonForExam,
    String? additionalNotes,
    bool? smoker,
    bool? alcohol,
    bool? physicallyActive,
    DateTime? timestamp,
  }) {
    return PatientDataModel(
      age: age ?? this.age,
      gender: gender ?? this.gender,
      height: height ?? this.height,
      weight: weight ?? this.weight,
      symptoms: symptoms ?? this.symptoms,
      medicalHistory: medicalHistory ?? this.medicalHistory,
      medications: medications ?? this.medications,
      familyHistory: familyHistory ?? this.familyHistory,
      symptomDuration: symptomDuration ?? this.symptomDuration,
      allergies: allergies ?? this.allergies,
      labResults: labResults ?? this.labResults,
      otherConditions: otherConditions ?? this.otherConditions,
      reasonForExam: reasonForExam ?? this.reasonForExam,
      additionalNotes: additionalNotes ?? this.additionalNotes,
      smoker: smoker ?? this.smoker,
      alcohol: alcohol ?? this.alcohol,
      physicallyActive: physicallyActive ?? this.physicallyActive,
      timestamp: timestamp ?? this.timestamp,
    );
  }

  @override
  List<Object?> get props => [
        age,
        gender,
        height,
        weight,
        symptoms,
        medicalHistory,
        medications,
        familyHistory,
        symptomDuration,
        allergies,
        labResults,
        otherConditions,
        reasonForExam,
        additionalNotes,
        smoker,
        alcohol,
        physicallyActive,
        timestamp,
      ];
}
