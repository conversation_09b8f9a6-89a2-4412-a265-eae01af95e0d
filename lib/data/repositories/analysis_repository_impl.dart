import 'dart:io';
import 'dart:convert';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:http/http.dart' as http;
import 'package:hekimmind/domain/repositories/analysis_repository.dart';
import 'package:hekimmind/data/models/analysis_result_model.dart';
import 'package:hekimmind/data/models/patient_data_model.dart';
import 'package:hekimmind/services/prompt_service.dart';
import 'package:hekimmind/core/config/app_config.dart';

class AnalysisRepositoryImpl implements AnalysisRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final String _openAIApiKey;

  AnalysisRepositoryImpl({
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
    required String openAIApiKey,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance,
        _openAIApiKey = openAIApiKey;

  @override
  Future<String> uploadImage(File image, String userId) async {
    final ref =
        _storage.ref().child('analysis_images/$userId/${DateTime.now()}.jpg');
    await ref.putFile(image);
    return await ref.getDownloadURL();
  }

  @override
  Future<AnalysisResultModel> analyzeImage(
    String imageUrl,
    String userId,
    String analysisType,
    PatientDataModel patientData,
  ) async {
    // Prompt oluştur
    final prompt = PromptService.generatePrompt(analysisType, patientData);

    // OpenAI API'ye istek gönder
    final requestBody = {
      'model': AppConfig.openAIModel,
      'messages': [
        {
          'role': 'user',
          'content': [
            {
              'type': 'text',
              'text': prompt,
            },
            {
              'type': 'image_url',
              'image_url': {
                'url': imageUrl,
              },
            },
          ],
        },
      ],
      'max_tokens': AppConfig.maxTokens,
      'temperature': 0.3,
    };

    final response = await http.post(
      Uri.parse('${AppConfig.apiBaseUrl}/chat/completions'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer ${AppConfig.openAIApiKey}',
      },
      body: jsonEncode(requestBody),
    );

    if (response.statusCode != 200) {
      throw Exception('OpenAI API hatası: ${response.statusCode} - ${response.body}');
    }

    final responseData = jsonDecode(response.body);
    final analysisResult = responseData['choices'][0]['message']['content'];

    final result = AnalysisResultModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      userId: userId,
      imageUrl: imageUrl,
      analysisType: analysisType,
      result: analysisResult,
      timestamp: DateTime.now(),
    );

    // Sonucu hasta bilgileri ile birlikte Firestore'a kaydet
    final analysisData = result.toJson();
    analysisData['patientData'] = patientData.toJson();
    analysisData['promptUsed'] = prompt;

    await _firestore.collection('medical_analyses').add(analysisData);

    return result;
  }



  @override
  Future<List<AnalysisResultModel>> getAnalysisHistory(String userId) async {
    // UserId kontrolü
    if (userId.isEmpty || userId == 'anonymous') {
      return []; // Boş liste döndür
    }

    final snapshot = await _firestore
        .collection('medical_analyses')
        .where('userId', isEqualTo: userId)
        .orderBy('timestamp', descending: true)
        .get();

    return snapshot.docs
        .map((doc) => AnalysisResultModel.fromJson(doc.data()))
        .toList();
  }
}
