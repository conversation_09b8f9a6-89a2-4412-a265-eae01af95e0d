import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:http/http.dart' as http;
import 'package:hekimmind/domain/repositories/analysis_repository.dart';
import 'package:hekimmind/data/models/analysis_result_model.dart';

class AnalysisRepositoryImpl implements AnalysisRepository {
  final FirebaseFirestore _firestore;
  final FirebaseStorage _storage;
  final String _openAIApiKey;

  AnalysisRepositoryImpl({
    FirebaseFirestore? firestore,
    FirebaseStorage? storage,
    required String openAIApiKey,
  })  : _firestore = firestore ?? FirebaseFirestore.instance,
        _storage = storage ?? FirebaseStorage.instance,
        _openAIApiKey = openAIApiKey;

  @override
  Future<String> uploadImage(File image, String userId) async {
    final ref =
        _storage.ref().child('analysis_images/$userId/${DateTime.now()}.jpg');
    await ref.putFile(image);
    return await ref.getDownloadURL();
  }

  @override
  Future<AnalysisResultModel> analyzeImage(
    String imageUrl,
    String userId,
    String analysisType,
  ) async {
    // OpenAI API'ye istek gönder
    final response = await http.post(
      Uri.parse('https://api.openai.com/v1/chat/completions'),
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer $_openAIApiKey',
      },
      body: {
        'model': 'gpt-4-vision-preview',
        'messages': [
          {
            'role': 'system',
            'content': _getPromptForAnalysisType(analysisType),
          },
          {
            'role': 'user',
            'content': [
              {
                'type': 'image_url',
                'image_url': imageUrl,
              },
            ],
          },
        ],
        'max_tokens': 500,
      },
    );

    if (response.statusCode != 200) {
      throw Exception('OpenAI API hatası: ${response.body}');
    }

    final result = AnalysisResultModel(
      id: DateTime.now().toString(),
      userId: userId,
      imageUrl: imageUrl,
      analysisType: analysisType,
      result: response.body, // OpenAI'dan gelen cevap
      timestamp: DateTime.now(),
    );

    // Sonucu Firestore'a kaydet
    await _firestore.collection('analysis_results').add(result.toJson());

    return result;
  }

  String _getPromptForAnalysisType(String analysisType) {
    switch (analysisType) {
      case 'MR':
        return 'Sen bir radyolog uzmansın. Bu MR görüntüsünü detaylı olarak incele ve bulgularını paylaş.';
      case 'EKG':
        return 'Sen bir kardiyolog uzmansın. Bu EKG görüntüsünü detaylı olarak incele ve bulgularını paylaş.';
      case 'EEG':
        return 'Sen bir nörolog uzmansın. Bu EEG görüntüsünü detaylı olarak incele ve bulgularını paylaş.';
      default:
        return 'Lütfen bu tıbbi görüntüyü detaylı olarak incele ve bulgularını paylaş.';
    }
  }

  @override
  Future<List<AnalysisResultModel>> getAnalysisHistory(String userId) async {
    final snapshot = await _firestore
        .collection('analysis_results')
        .where('userId', isEqualTo: userId)
        .orderBy('timestamp', descending: true)
        .get();

    return snapshot.docs
        .map((doc) => AnalysisResultModel.fromJson(doc.data()))
        .toList();
  }
}
