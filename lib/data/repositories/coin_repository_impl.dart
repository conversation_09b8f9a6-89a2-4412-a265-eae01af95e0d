import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:hekimmind/domain/repositories/coin_repository.dart';
import 'package:hekimmind/data/models/coin_transaction_model.dart';

class CoinRepositoryImpl implements CoinRepository {
  final FirebaseFirestore _firestore;

  CoinRepositoryImpl({FirebaseFirestore? firestore})
      : _firestore = firestore ?? FirebaseFirestore.instance;

  @override
  Future<int> getCurrentBalance(String userId) async {
    final doc = await _firestore.collection('users').doc(userId).get();
    return doc.data()?['coinBalance'] as int? ?? 0;
  }

  @override
  Future<void> addCoins(String userId, int amount) async {
    final userRef = _firestore.collection('users').doc(userId);

    await _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(userRef);
      final currentBalance = snapshot.data()?['coinBalance'] as int? ?? 0;

      transaction.update(userRef, {'coinBalance': currentBalance + amount});

      // İşlem kaydı oluştur
      final transactionRef = _firestore.collection('coinTransactions').doc();
      transaction.set(
          transactionRef,
          CoinTransactionModel(
            id: transactionRef.id,
            userId: userId,
            amount: amount,
            type: 'purchase',
            timestamp: DateTime.now(),
          ).toJson());
    });
  }

  @override
  Future<void> useCoins(String userId, int amount) async {
    final userRef = _firestore.collection('users').doc(userId);

    await _firestore.runTransaction((transaction) async {
      final snapshot = await transaction.get(userRef);
      final currentBalance = snapshot.data()?['coinBalance'] as int? ?? 0;

      if (currentBalance < amount) {
        throw Exception('Yetersiz kredi bakiyesi');
      }

      transaction.update(userRef, {'coinBalance': currentBalance - amount});

      // İşlem kaydı oluştur
      final transactionRef = _firestore.collection('coinTransactions').doc();
      transaction.set(
          transactionRef,
          CoinTransactionModel(
            id: transactionRef.id,
            userId: userId,
            amount: -amount,
            type: 'usage',
            timestamp: DateTime.now(),
          ).toJson());
    });
  }

  @override
  Future<List<CoinTransactionModel>> getTransactionHistory(
      String userId) async {
    final snapshot = await _firestore
        .collection('coinTransactions')
        .where('userId', isEqualTo: userId)
        .orderBy('timestamp', descending: true)
        .get();

    return snapshot.docs
        .map((doc) => CoinTransactionModel.fromJson(doc.data()))
        .toList();
  }

  @override
  Stream<int> getCoinBalanceStream(String userId) {
    return _firestore
        .collection('users')
        .doc(userId)
        .snapshots()
        .map((snapshot) => snapshot.data()?['coinBalance'] as int? ?? 0);
  }
}
