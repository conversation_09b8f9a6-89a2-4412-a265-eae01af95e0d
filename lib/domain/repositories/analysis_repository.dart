import 'dart:io';
import 'package:hekimmind/data/models/analysis_result_model.dart';
import 'package:hekimmind/data/models/patient_data_model.dart';

abstract class AnalysisRepository {
  Future<String> uploadImage(File image, String userId);
  Future<AnalysisResultModel> analyzeImage(
    String imageUrl,
    String userId,
    String analysisType,
    PatientDataModel patientData,
  );
  Future<List<AnalysisResultModel>> getAnalysisHistory(String userId);
}
